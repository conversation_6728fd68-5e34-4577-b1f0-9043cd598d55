import { apiService } from './api'

// Session Types
export interface SessionStatus {
  session_id: number
  session_token: string
  is_locked: boolean
  locked_at: string | null
  lock_reason: string | null
  last_activity: string
  idle_timeout_minutes: number
  expires_at: string
  should_be_locked: boolean
  is_valid: boolean
}

export interface SessionUnlockRequest {
  method: 'pin' | 'biometric'
  pin?: string
  biometric_data?: any
}

export interface SessionUnlockResponse {
  message: string
  unlocked_at: string
  method_used: string
}

/**
 * Session Management Service
 * Handles server-side session lock/unlock operations
 */
class SessionService {
  
  /**
   * Get current session status from server
   */
  async getSessionStatus(): Promise<SessionStatus> {
    return await apiService.get<SessionStatus>('/session/status')
  }

  /**
   * Update session activity timestamp
   */
  async updateActivity(): Promise<{ last_activity: string; message: string }> {
    return await apiService.post<{ last_activity: string; message: string }>('/session/activity')
  }

  /**
   * Lock current session
   */
  async lockSession(reason: 'inactivity' | 'admin_action' | 'security_breach' | 'manual' = 'manual'): Promise<{ message: string; locked_at: string; lock_reason: string }> {
    return await apiService.post<{ message: string; locked_at: string; lock_reason: string }>('/session/lock', { reason })
  }

  /**
   * Unlock session with PIN or biometric verification
   */
  async unlockSession(unlockData: SessionUnlockRequest): Promise<SessionUnlockResponse> {
    return await apiService.post<SessionUnlockResponse>('/session/unlock', unlockData)
  }

  /**
   * Check if session should be locked based on activity
   */
  async checkSessionLock(): Promise<SessionStatus> {
    try {
      const status = await this.getSessionStatus()
      
      // If session should be locked but isn't, the server will auto-lock it
      if (status.should_be_locked && !status.is_locked) {
        // Refresh status to get updated lock state
        return await this.getSessionStatus()
      }
      
      return status
    } catch (error: any) {
      // If we get a 423 (Locked) response, the session is locked
      if (error.response?.status === 423) {
        throw new Error('Session is locked')
      }
      throw error
    }
  }

  /**
   * Start periodic activity updates
   */
  startActivityTracking(intervalMinutes: number = 2): () => void {
    const intervalMs = intervalMinutes * 60 * 1000
    
    const updateActivity = async () => {
      // Don't make requests when offline
      if (!navigator.onLine) {
        console.log('🌐 Offline - skipping session activity update')
        return
      }

      try {
        await this.updateActivity()
        console.log('🔄 Session activity updated')
      } catch (error: any) {
        // If session is locked, stop tracking
        if (error.response?.status === 423) {
          console.log('🔒 Session is locked, stopping activity tracking')
          clearInterval(intervalId)
          // Emit session locked event
          window.dispatchEvent(new CustomEvent('session-locked'))
        } else if (error.code === 'OFFLINE' || error.code === 'NETWORK_ERROR' || !navigator.onLine) {
          console.log('🌐 Network error during activity update - will retry when online')
        } else {
          console.warn('Failed to update session activity:', error.message)
        }
      }
    }

    // Update activity immediately
    updateActivity()
    
    // Set up periodic updates
    const intervalId = setInterval(updateActivity, intervalMs)
    
    // Return cleanup function
    return () => {
      clearInterval(intervalId)
      console.log('🛑 Session activity tracking stopped')
    }
  }

  /**
   * Monitor session status and auto-lock if needed
   */
  startSessionMonitoring(checkIntervalMinutes: number = 1): () => void {
    const intervalMs = checkIntervalMinutes * 60 * 1000
    
    const checkSession = async () => {
      // Don't make requests when offline
      if (!navigator.onLine) {
        console.log('🌐 Offline - skipping session lock check')
        return
      }

      try {
        const status = await this.checkSessionLock()

        if (status.is_locked) {
          console.log('🔒 Session is locked')
          clearInterval(intervalId)
          // Emit session locked event
          window.dispatchEvent(new CustomEvent('session-locked'))
        }
      } catch (error: any) {
        if (error.message === 'Session is locked' || error.status === 423) {
          console.log('🔒 Session lock detected during monitoring')
          clearInterval(intervalId)
          // Emit session locked event
          window.dispatchEvent(new CustomEvent('session-locked'))
        } else if (error.code === 'OFFLINE' || error.code === 'NETWORK_ERROR' || !navigator.onLine) {
          console.log('🌐 Network error during session monitoring - will retry when online')
        } else {
          console.warn('Session monitoring error:', error.message)
        }
      }
    }

    // Check session immediately
    checkSession()
    
    // Set up periodic checks
    const intervalId = setInterval(checkSession, intervalMs)

    // Listen for online events to resume monitoring
    const handleOnline = () => {
      console.log('🌐 Back online - checking session status')
      // Check session immediately when coming back online
      checkSession()
    }

    window.addEventListener('online', handleOnline)

    // Return cleanup function
    return () => {
      clearInterval(intervalId)
      window.removeEventListener('online', handleOnline)
      console.log('🛑 Session monitoring stopped')
    }
  }

  /**
   * Handle session lock response from API calls
   */
  handleSessionLockResponse(error: any): boolean {
    if (error.response?.status === 423) {
      // Session is locked
      console.log('🔒 API call blocked - session is locked')
      window.dispatchEvent(new CustomEvent('session-locked'))
      return true
    }
    return false
  }

  /**
   * Verify PIN for session unlock
   */
  async verifyPinForUnlock(pin: string): Promise<boolean> {
    try {
      await this.unlockSession({ method: 'pin', pin })
      return true
    } catch (error: any) {
      console.error('PIN verification failed:', error.message)
      return false
    }
  }

  /**
   * Verify biometric for session unlock
   */
  async verifyBiometricForUnlock(biometricData: any): Promise<boolean> {
    try {
      await this.unlockSession({ method: 'biometric', biometric_data: biometricData })
      return true
    } catch (error: any) {
      console.error('Biometric verification failed:', error.message)
      return false
    }
  }

  /**
   * Get session timeout information
   */
  async getSessionTimeout(): Promise<{ idle_timeout_minutes: number; expires_at: string }> {
    const status = await this.getSessionStatus()
    return {
      idle_timeout_minutes: status.idle_timeout_minutes,
      expires_at: status.expires_at
    }
  }

  /**
   * Check if user is online (for offline session lock bypass)
   */
  isOnline(): boolean {
    return navigator.onLine
  }

  /**
   * Get time since last activity (in minutes)
   */
  async getTimeSinceLastActivity(): Promise<number> {
    const status = await this.getSessionStatus()
    const lastActivity = new Date(status.last_activity)
    const now = new Date()
    const diffMs = now.getTime() - lastActivity.getTime()
    return Math.floor(diffMs / (1000 * 60)) // Convert to minutes
  }
}

// Export singleton instance
export const sessionService = new SessionService()
export default sessionService
